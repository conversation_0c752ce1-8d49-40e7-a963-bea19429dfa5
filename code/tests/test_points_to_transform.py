"""Unit tests for points_to_transform module."""

import pytest
import numpy as np
from numpy.testing import assert_allclose
import open3d as o3d

from calibration.points_to_transform import get_transform, invert_transform


class TestGetTransform:
    """Test cases for get_transform function."""

    def test_simple_identity_case(self):
        """Test get_transform with simple orthogonal unit vectors."""
        origin = np.array([0.0, 0.0, 0.0])
        x_direction = np.array([1.0, 0.0, 0.0])
        y_direction = np.array([0.0, 1.0, 0.0])

        transform = get_transform(origin, x_direction, y_direction)

        # Check that it's a 4x4 matrix
        assert transform.shape == (4, 4)

        # Check the rotation part (first 3x3)
        expected_rotation = np.array([[1.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 1.0]])
        assert_allclose(transform[:3, :3], expected_rotation, atol=1e-10)

        # Check the translation part
        assert_allclose(transform[:3, 3], origin, atol=1e-10)

        # Check the bottom row
        assert_allclose(transform[3, :], [0.0, 0.0, 0.0, 1.0], atol=1e-10)

    def test_non_zero_origin(self):
        """Test get_transform with non-zero origin."""
        origin = np.array([1.0, 2.0, 3.0])
        x_direction = np.array([1.0, 0.0, 0.0])
        y_direction = np.array([0.0, 1.0, 0.0])

        transform = get_transform(origin, x_direction, y_direction)

        # Check the translation part
        assert_allclose(transform[:3, 3], origin, atol=1e-10)

    def test_orthogonalization(self):
        """Test that y_direction is properly orthogonalized to x_direction."""
        origin = np.array([0.0, 0.0, 0.0])
        x_direction = np.array([1.0, 0.0, 0.0])
        # y_direction has an x component that should be removed
        y_direction = np.array([0.5, 1.0, 0.0])

        transform = get_transform(origin, x_direction, y_direction)

        # The resulting x and y vectors should be orthogonal
        x_vec = transform[:3, 0]
        y_vec = transform[:3, 1]

        # Check orthogonality (dot product should be zero)
        assert_allclose(np.dot(x_vec, y_vec), 0.0, atol=1e-10)

        # Check that both are unit vectors
        assert_allclose(np.linalg.norm(x_vec), 1.0, atol=1e-10)
        assert_allclose(np.linalg.norm(y_vec), 1.0, atol=1e-10)

    def test_normalization(self):
        """Test that direction vectors are properly normalized."""
        origin = np.array([0.0, 0.0, 0.0])
        x_direction = np.array([3.0, 0.0, 0.0])  # Not unit length
        y_direction = np.array([0.0, 2.0, 0.0])  # Not unit length

        transform = get_transform(origin, x_direction, y_direction)

        # All direction vectors should be unit length
        x_vec = transform[:3, 0]
        y_vec = transform[:3, 1]
        z_vec = transform[:3, 2]

        assert_allclose(np.linalg.norm(x_vec), 1.0, atol=1e-10)
        assert_allclose(np.linalg.norm(y_vec), 1.0, atol=1e-10)
        assert_allclose(np.linalg.norm(z_vec), 1.0, atol=1e-10)

    def test_right_handed_coordinate_system(self):
        """Test that the resulting coordinate system is right-handed."""
        origin = np.array([0.0, 0.0, 0.0])
        x_direction = np.array([1.0, 0.0, 0.0])
        y_direction = np.array([0.0, 1.0, 0.0])

        transform = get_transform(origin, x_direction, y_direction)

        x_vec = transform[:3, 0]
        y_vec = transform[:3, 1]
        z_vec = transform[:3, 2]

        # z should be x cross y for right-handed system
        expected_z = np.cross(x_vec, y_vec)
        assert_allclose(z_vec, expected_z, atol=1e-10)

    def test_arbitrary_directions(self):
        """Test with arbitrary direction vectors."""
        origin = np.array([1.0, -2.0, 3.0])
        x_direction = np.array([1.0, -1.0, 0.0])
        y_direction = np.array([1.0, 1.0, 1.0])

        transform = get_transform(origin, x_direction, y_direction)

        # Basic checks
        assert transform.shape == (4, 4)
        assert_allclose(transform[:3, 3], origin, atol=1e-10)
        assert_allclose(transform[3, :], [0.0, 0.0, 0.0, 1.0], atol=1e-10)

        # Check orthogonality and normalization
        x_vec = transform[:3, 0]
        y_vec = transform[:3, 1]
        z_vec = transform[:3, 2]

        assert_allclose(np.dot(x_vec, y_vec), 0.0, atol=1e-10)
        assert_allclose(np.dot(x_vec, z_vec), 0.0, atol=1e-10)
        assert_allclose(np.dot(y_vec, z_vec), 0.0, atol=1e-10)

        assert_allclose(np.linalg.norm(x_vec), 1.0, atol=1e-10)
        assert_allclose(np.linalg.norm(y_vec), 1.0, atol=1e-10)
        assert_allclose(np.linalg.norm(z_vec), 1.0, atol=1e-10)

    def test_no_scaling_in_transform(self):
        """Test that the transformation matrix contains no scaling."""
        origin = np.array([1.0, 2.0, 3.0])
        x_direction = np.array([10.0, 0.0, 0.0])  # Large magnitude
        y_direction = np.array([0.0, 5.0, 0.0])  # Large magnitude

        transform = get_transform(origin, x_direction, y_direction)

        # Extract the rotation part (3x3 upper-left)
        rotation_matrix = transform[:3, :3]

        # For a pure rotation matrix, the determinant should be 1
        det = np.linalg.det(rotation_matrix)
        assert_allclose(det, 1.0, atol=1e-10)

        # All columns should be unit vectors (no scaling)
        for i in range(3):
            column_norm = np.linalg.norm(rotation_matrix[:, i])
            assert_allclose(column_norm, 1.0, atol=1e-10)

    def test_parallel_input_vectors_error(self):
        """Test behavior when input vectors are parallel."""
        origin = np.array([0.0, 0.0, 0.0])
        x_direction = np.array([1.0, 0.0, 0.0])
        y_direction = np.array([2.0, 0.0, 0.0])  # Parallel to x_direction

        # Should raise ValueError for parallel vectors
        with pytest.raises(ValueError, match="cannot be parallel"):
            get_transform(origin, x_direction, y_direction)

    def test_zero_vector_input_error(self):
        """Test behavior with zero vector inputs."""
        origin = np.array([0.0, 0.0, 0.0])

        # Test with zero x_direction
        with pytest.raises(ValueError, match="x_direction cannot be a zero vector"):
            get_transform(origin, np.array([0.0, 0.0, 0.0]), np.array([1.0, 0.0, 0.0]))

        # Test with zero y_direction
        with pytest.raises(ValueError, match="y_direction cannot be a zero vector"):
            get_transform(origin, np.array([1.0, 0.0, 0.0]), np.array([0.0, 0.0, 0.0]))

    def test_specific_no_scaling_assertion(self):
        """Specific test to ensure get_transform never returns scaling transformations."""
        # Test multiple random cases to ensure no scaling ever occurs
        np.random.seed(42)  # For reproducible tests

        for _ in range(10):
            # Generate random vectors with various magnitudes
            origin = np.random.uniform(-10, 10, 3)
            x_direction = np.random.uniform(-100, 100, 3)
            y_direction = np.random.uniform(-100, 100, 3)

            # Ensure vectors are not zero or parallel
            while np.allclose(x_direction, 0) or np.allclose(y_direction, 0):
                x_direction = np.random.uniform(-100, 100, 3)
                y_direction = np.random.uniform(-100, 100, 3)

            # Ensure not parallel by checking if cross product is non-zero
            if np.allclose(np.cross(x_direction, y_direction), 0):
                y_direction[0] += 1.0  # Make them non-parallel

            transform = get_transform(origin, x_direction, y_direction)
            rotation = transform[:3, :3]

            # Check that all columns are unit vectors (no scaling)
            for i in range(3):
                column_norm = np.linalg.norm(rotation[:, i])
                assert_allclose(
                    column_norm,
                    1.0,
                    atol=1e-10,
                    err_msg=f"Column {i} is not unit length: {column_norm}",
                )

            # Check that determinant is 1 (no scaling, no reflection)
            det = np.linalg.det(rotation)
            assert_allclose(det, 1.0, atol=1e-10, err_msg=f"Determinant is not 1: {det}")

            # Check orthogonality (R * R^T = I)
            should_be_identity = rotation @ rotation.T
            assert_allclose(
                should_be_identity,
                np.eye(3),
                atol=1e-10,
                err_msg="Rotation matrix is not orthogonal",
            )

    def test_correct_results(self):
        """Test that the transformation matrix works correctly."""
        origin = np.array([1.0, 2.0, 3.0])
        x_direction = np.array([0.0, 0.0, 1.0])
        y_direction = np.array([0.0, 2.5, 0.0])

        transform = get_transform(origin, x_direction, y_direction)

        def T(vector):
            return (transform @ np.array([*vector, 1.0]))[:3]

        # Check actual expected results
        assert np.allclose(T([0.0, 0.0, 0.0]), origin, atol=1e-10)
        assert np.allclose(T([1.0, 0.0, 0.0]), origin + x_direction, atol=1e-10)
        assert np.allclose(
            T([0.0, 1.0, 0.0]), origin + y_direction / np.linalg.norm(y_direction), atol=1e-10
        )
        should_be_z = np.cross(x_direction, y_direction)
        should_be_z /= np.linalg.norm(should_be_z)
        assert np.allclose(T([0.0, 0.0, 1.0]), origin + should_be_z, atol=1e-10)
        assert np.allclose(
            T([1.5, -1.0, 2.0]),
            origin
            + 1.5 * x_direction
            - (y_direction / np.linalg.norm(y_direction))
            + 2 * should_be_z,
            atol=1e-10,
        )


class TestGetTransformOpen3DIntegration:
    """Test cases for get_transform integration with Open3D."""

    def test_transform_point_cloud(self):
        """Test that transformation matrix works correctly with Open3D point clouds."""
        # Create a simple transformation
        origin = np.array([1.0, 2.0, 3.0])
        x_direction = np.array([1.0, 0.0, 0.0])
        y_direction = np.array([0.0, 1.0, 0.0])

        transform = get_transform(origin, x_direction, y_direction)

        # Create a simple point cloud (unit cube vertices)
        points = np.array(
            [
                [0.0, 0.0, 0.0],  # Origin
                [1.0, 0.0, 0.0],  # X direction
                [0.0, 1.0, 0.0],  # Y direction
                [0.0, 0.0, 1.0],  # Z direction
                [1.0, 1.0, 1.0],  # Corner
            ]
        )

        # Create Open3D point cloud
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)

        # Apply transformation
        pcd_transformed = pcd.transform(transform)

        # Get transformed points
        transformed_points = np.asarray(pcd_transformed.points)

        # Check that origin point is transformed correctly
        assert_allclose(transformed_points[0], origin, atol=1e-10)

        # Check that unit vectors are transformed correctly
        # X direction should become origin + x_axis
        expected_x = origin + transform[:3, 0]
        assert_allclose(transformed_points[1], expected_x, atol=1e-10)

        # Y direction should become origin + y_axis
        expected_y = origin + transform[:3, 1]
        assert_allclose(transformed_points[2], expected_y, atol=1e-10)

        # Z direction should become origin + z_axis
        expected_z = origin + transform[:3, 2]
        assert_allclose(transformed_points[3], expected_z, atol=1e-10)

    def test_transform_mesh_geometry(self):
        """Test that transformation matrix works correctly with Open3D mesh geometries."""
        # Create transformation with rotation
        origin = np.array([2.0, -1.0, 0.5])
        x_direction = np.array([1.0, -1.0, 0.0])  # 45-degree rotation in XY plane
        y_direction = np.array([1.0, 1.0, 0.0])  # Will be orthogonalized

        transform = get_transform(origin, x_direction, y_direction)

        # Create a simple mesh (unit cube)
        mesh = o3d.geometry.TriangleMesh.create_box(1.0, 1.0, 1.0)

        # Get original vertices
        original_vertices = np.asarray(mesh.vertices)

        # Apply transformation
        mesh_transformed = mesh.transform(transform)

        # Get transformed vertices
        transformed_vertices = np.asarray(mesh_transformed.vertices)

        # Verify transformation properties
        # 1. Number of vertices should remain the same
        assert len(transformed_vertices) == len(original_vertices)

        # 2. Distances between vertices should be preserved (rigid transformation)
        for i in range(len(original_vertices)):
            for j in range(i + 1, len(original_vertices)):
                original_distance = np.linalg.norm(original_vertices[i] - original_vertices[j])
                transformed_distance = np.linalg.norm(
                    transformed_vertices[i] - transformed_vertices[j]
                )
                assert_allclose(
                    original_distance,
                    transformed_distance,
                    atol=1e-10,
                    err_msg=f"Distance between vertices {i} and {j} changed after transformation",
                )

    def test_coordinate_frame_transformation(self):
        """Test transformation with Open3D coordinate frame."""
        # Create a coordinate frame transformation
        origin = np.array([0.0, 0.0, 1.0])
        x_direction = np.array([1.0, 0.0, 0.0])  # X stays X
        y_direction = np.array([0.0, 0.0, 1.0])  # Z becomes Y

        transform = get_transform(origin, x_direction, y_direction)

        # Create coordinate frame mesh
        coord_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1.0)

        # Apply transformation
        coord_frame_transformed = coord_frame.transform(transform)

        # The transformation should be valid (no NaN or infinite values)
        vertices = np.asarray(coord_frame_transformed.vertices)
        assert not np.isnan(vertices).any(), "Transformed coordinate frame contains NaN values"
        assert not np.isinf(vertices).any(), (
            "Transformed coordinate frame contains infinite values"
        )

        # Check that the transformation preserves the coordinate frame structure
        assert len(vertices) > 0, "Coordinate frame has no vertices after transformation"

    def test_point_cloud_with_colors(self):
        """Test transformation of point cloud with colors (like from RealSense camera)."""
        # Create transformation similar to conveyor calibration
        origin = np.array([0.1, -0.05, 0.8])  # Typical camera position
        x_direction = np.array([1.0, 0.0, 0.0])  # Conveyor width direction
        y_direction = np.array([0.0, 1.0, 0.1])  # Slightly tilted conveyor

        transform = get_transform(origin, x_direction, y_direction)

        # Create point cloud with colors (simulating camera data)
        points = np.random.uniform(-0.5, 0.5, (100, 3))  # Random points
        colors = np.random.uniform(0, 1, (100, 3))  # Random colors

        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        pcd.colors = o3d.utility.Vector3dVector(colors)

        # Apply transformation
        pcd_transformed = pcd.transform(transform)

        # Check that colors are preserved
        original_colors = np.asarray(pcd.colors)
        transformed_colors = np.asarray(pcd_transformed.colors)
        assert_allclose(
            original_colors,
            transformed_colors,
            atol=1e-10,
            err_msg="Colors should be preserved during transformation",
        )

        # Check that point count is preserved
        assert len(pcd_transformed.points) == len(points)

        # Check that transformation is rigid (distances preserved)
        original_points = np.asarray(pcd.points)
        transformed_points = np.asarray(pcd_transformed.points)

        # Test a few random point pairs
        for _ in range(10):
            i, j = np.random.choice(len(points), 2, replace=False)
            original_dist = np.linalg.norm(original_points[i] - original_points[j])
            transformed_dist = np.linalg.norm(transformed_points[i] - transformed_points[j])
            assert_allclose(
                original_dist,
                transformed_dist,
                atol=1e-10,
                err_msg=f"Distance between points {i} and {j} changed",
            )

    def test_multiple_geometry_transformation(self):
        """Test transformation of multiple geometries together."""
        # Create transformation
        origin = np.array([1.0, 0.0, 0.0])
        x_direction = np.array([1.0, 0.0, 0.0])
        y_direction = np.array([0.0, 1.0, 1.0])  # 45-degree tilt

        transform = get_transform(origin, x_direction, y_direction)

        # Create multiple geometries
        sphere = o3d.geometry.TriangleMesh.create_sphere(radius=0.5)
        cylinder = o3d.geometry.TriangleMesh.create_cylinder(radius=0.3, height=1.0)
        box = o3d.geometry.TriangleMesh.create_box(0.5, 0.5, 0.5)

        geometries = [sphere, cylinder, box]

        # Store original centroids
        original_centroids = []
        for geom in geometries:
            vertices = np.asarray(geom.vertices)
            centroid = np.mean(vertices, axis=0)
            original_centroids.append(centroid)

        # Apply transformation to all geometries
        transformed_geometries = []
        for geom in geometries:
            transformed_geom = geom.transform(transform)
            transformed_geometries.append(transformed_geom)

        # Check transformed centroids
        for i, geom in enumerate(transformed_geometries):
            vertices = np.asarray(geom.vertices)
            transformed_centroid = np.mean(vertices, axis=0)

            # Expected centroid after transformation
            expected_centroid = transform[:3, :3] @ original_centroids[i] + transform[:3, 3]

            assert_allclose(
                transformed_centroid,
                expected_centroid,
                atol=1e-10,
                err_msg=f"Geometry {i} centroid not transformed correctly",
            )

    def test_conveyor_calibration_scenario(self):
        """Test a realistic conveyor calibration scenario with Open3D."""
        # Simulate ArUco marker positions (typical conveyor setup)
        upstream_marker_id = np.array([0.0, -0.3, 0.75])  # Upstream marker
        width_marker_id = np.array([0.2, -0.3, 0.75])  # Width marker
        downstream_marker_id = np.array([0.0, 0.3, 0.75])  # Downstream marker

        # Create transformation as done in conveyor_calibration.py
        origin = upstream_marker_id
        x_direction = width_marker_id - upstream_marker_id
        y_direction = downstream_marker_id - upstream_marker_id

        transform = get_transform(origin, x_direction, y_direction)

        # Create a point cloud representing objects on the conveyor
        conveyor_points = np.array(
            [
                [0.0, 0.0, 0.0],  # At upstream marker
                [0.1, 0.0, 0.0],  # 10cm along width
                [0.0, 0.2, 0.0],  # 20cm downstream
                [0.1, 0.2, 0.0],  # Corner
            ]
        )

        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(conveyor_points)

        # Transform to camera coordinate system
        pcd_camera = pcd.transform(transform)
        camera_points = np.asarray(pcd_camera.points)

        # Verify key transformations
        # Origin should map to upstream marker position
        assert_allclose(
            camera_points[0],
            upstream_marker_id,
            atol=1e-10,
            err_msg="Origin not mapped to upstream marker correctly",
        )

        # Width direction should be preserved
        width_vector_transformed = camera_points[1] - camera_points[0]
        expected_width_vector = transform[:3, 0] * 0.1  # Transform x-axis by 0.1
        assert_allclose(
            width_vector_transformed,
            expected_width_vector,
            atol=1e-10,
            err_msg="Width direction not preserved correctly",
        )

        # All points should be at approximately the same Z height (conveyor surface)
        z_heights = camera_points[:, 2]
        assert_allclose(
            z_heights,
            upstream_marker_id[2],
            atol=1e-10,
            err_msg="Points not on conveyor surface after transformation",
        )


class TestInvertTransform:
    """Test cases for invert_transform function."""

    def test_known_values_identity_transform(self):
        """Test invert_transform with known identity transformation matrix."""
        # Identity transformation matrix
        identity_transform = np.eye(4)

        # Invert the identity matrix
        inverted = invert_transform(identity_transform)

        # Should get back the identity matrix
        expected = np.eye(4)
        assert_allclose(inverted, expected, atol=1e-10)

    def test_known_values_translation_only(self):
        """Test invert_transform with known translation-only transformation."""
        # Translation-only transformation: translate by [1, 2, 3]
        transform = np.array(
            [
                [1.0, 0.0, 0.0, 1.0],
                [0.0, 1.0, 0.0, 2.0],
                [0.0, 0.0, 1.0, 3.0],
                [0.0, 0.0, 0.0, 1.0],
            ]
        )

        # Invert the transformation
        inverted = invert_transform(transform)

        # Expected inverse: translate by [-1, -2, -3]
        expected = np.array(
            [
                [1.0, 0.0, 0.0, -1.0],
                [0.0, 1.0, 0.0, -2.0],
                [0.0, 0.0, 1.0, -3.0],
                [0.0, 0.0, 0.0, 1.0],
            ]
        )

        assert_allclose(inverted, expected, atol=1e-10)

    def test_known_values_rotation_90_degrees(self):
        """Test invert_transform with known 90-degree rotation around Z-axis."""
        # 90-degree rotation around Z-axis with translation [1, 2, 3]
        transform = np.array(
            [
                [0.0, -1.0, 0.0, 1.0],
                [1.0, 0.0, 0.0, 2.0],
                [0.0, 0.0, 1.0, 3.0],
                [0.0, 0.0, 0.0, 1.0],
            ]
        )

        # Invert the transformation
        inverted = invert_transform(transform)

        # Expected inverse: -90-degree rotation around Z-axis with adjusted translation
        # R^T = [[0, 1, 0], [-1, 0, 0], [0, 0, 1]]
        # -R^T @ p = -[[0, 1, 0], [-1, 0, 0], [0, 0, 1]] @ [1, 2, 3] = -[2, -1, 3] = [-2, 1, -3]
        expected = np.array(
            [
                [0.0, 1.0, 0.0, -2.0],
                [-1.0, 0.0, 0.0, 1.0],
                [0.0, 0.0, 1.0, -3.0],
                [0.0, 0.0, 0.0, 1.0],
            ]
        )

        assert_allclose(inverted, expected, atol=1e-10)

    def test_identity_by_transform_and_inverse(self):
        """Test that transforming and then inverse transforming gives identity."""
        # Create a transformation using get_transform
        origin = np.array([1.5, -2.3, 4.7])
        x_direction = np.array([0.6, 0.8, 0.0])  # Not unit vector
        y_direction = np.array([-0.4, 0.3, 0.8])  # Not orthogonal to x_direction

        transform = get_transform(origin, x_direction, y_direction)

        # Invert the transformation
        inverted = invert_transform(transform)

        # T @ T^-1 should equal identity
        identity_result = transform @ inverted
        expected_identity = np.eye(4)
        assert_allclose(identity_result, expected_identity, atol=1e-10)

        # T^-1 @ T should also equal identity
        identity_result_2 = inverted @ transform
        assert_allclose(identity_result_2, expected_identity, atol=1e-10)

    def test_point_transformation_roundtrip(self):
        """Test that points transformed and inverse transformed return to original."""
        # Create a transformation
        origin = np.array([2.0, 1.0, -1.0])
        x_direction = np.array([1.0, 1.0, 0.0])
        y_direction = np.array([0.0, 1.0, 1.0])

        transform = get_transform(origin, x_direction, y_direction)
        inverted = invert_transform(transform)

        # Test points
        test_points = np.array(
            [
                [0.0, 0.0, 0.0, 1.0],  # Origin
                [1.0, 0.0, 0.0, 1.0],  # X direction
                [0.0, 1.0, 0.0, 1.0],  # Y direction
                [0.0, 0.0, 1.0, 1.0],  # Z direction
                [2.5, -1.3, 0.7, 1.0],  # Random point
            ]
        )

        # Transform points
        transformed_points = (transform @ test_points.T).T

        # Inverse transform back
        roundtrip_points = (inverted @ transformed_points.T).T

        # Should get back original points
        assert_allclose(roundtrip_points, test_points, atol=1e-10)

    def test_error_non_rotation_matrix_determinant(self):
        """Test error when transformation matrix doesn't have determinant 1."""
        # Create a matrix with scaling (determinant != 1)
        transform_with_scaling = np.array(
            [
                [2.0, 0.0, 0.0, 1.0],  # Scaling by 2 in x
                [0.0, 1.0, 0.0, 2.0],
                [0.0, 0.0, 1.0, 3.0],
                [0.0, 0.0, 0.0, 1.0],
            ]
        )

        with pytest.raises(
            ValueError, match="Transformation matrix must be a pure rotation matrix"
        ):
            invert_transform(transform_with_scaling)

    def test_error_invalid_homogeneous_matrix_bottom_right(self):
        """Test error when bottom-right element is not 1.0."""
        # Create matrix with invalid bottom-right element
        invalid_transform = np.array(
            [
                [1.0, 0.0, 0.0, 1.0],
                [0.0, 1.0, 0.0, 2.0],
                [0.0, 0.0, 1.0, 3.0],
                [0.0, 0.0, 0.0, 2.0],  # Should be 1.0
            ]
        )

        with pytest.raises(ValueError, match="Transformation matrix must be a homogeneous matrix"):
            invert_transform(invalid_transform)

    def test_error_invalid_homogeneous_matrix_bottom_row(self):
        """Test error when bottom row has non-zero values in first three elements."""
        # Create matrix with invalid bottom row
        invalid_transform = np.array(
            [
                [1.0, 0.0, 0.0, 1.0],
                [0.0, 1.0, 0.0, 2.0],
                [0.0, 0.0, 1.0, 3.0],
                [1.0, 0.0, 0.0, 1.0],  # First three should be 0.0
            ]
        )

        with pytest.raises(ValueError, match="Transformation matrix must be a homogeneous matrix"):
            invert_transform(invalid_transform)

    def test_error_reflection_matrix(self):
        """Test error when transformation matrix contains reflection (determinant = -1)."""
        # Create a reflection matrix (determinant = -1)
        reflection_transform = np.array(
            [
                [-1.0, 0.0, 0.0, 1.0],  # Reflection in x
                [0.0, 1.0, 0.0, 2.0],
                [0.0, 0.0, 1.0, 3.0],
                [0.0, 0.0, 0.0, 1.0],
            ]
        )

        with pytest.raises(
            ValueError, match="Transformation matrix must be a pure rotation matrix"
        ):
            invert_transform(reflection_transform)
