import unittest
import numpy as np
import open3d as o3d
from box_top.box_top_detector import apply_voxel_clustering


class TestVoxelClustering(unittest.TestCase):
    """Test cases for the voxel clustering algorithm."""

    def test_single_connected_box(self):
        """Test clustering with a single connected box near the scan line."""
        # Create a simple box-like point cloud centered around scan line
        scan_line_x = 0.5
        voxel_size = 0.05
        
        # Generate points for a box around the scan line
        points = []
        for x in np.arange(0.45, 0.55, 0.01):  # Around scan line
            for y in np.arange(0.2, 0.3, 0.01):
                for z in np.arange(0.35, 0.37, 0.01):
                    points.append([x, y, z])
        
        points = np.array(points)
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        
        # Apply clustering
        filtered_pcd = apply_voxel_clustering(pcd, scan_line_x, voxel_size)
        filtered_points = np.asarray(filtered_pcd.points)
        
        # Should keep all points since they're all connected
        self.assertGreater(len(filtered_points), 0)
        self.assertEqual(len(filtered_points), len(points))

    def test_two_separate_boxes(self):
        """Test clustering with two separate boxes, one near scan line."""
        scan_line_x = 0.5
        voxel_size = 0.05
        
        # Create two separate boxes
        points = []
        
        # Box 1: Near scan line (should be kept)
        for x in np.arange(0.45, 0.55, 0.02):
            for y in np.arange(0.2, 0.3, 0.02):
                for z in np.arange(0.35, 0.37, 0.02):
                    points.append([x, y, z])
        
        box1_count = len(points)
        
        # Box 2: Far from scan line (should be filtered out)
        for x in np.arange(0.8, 0.9, 0.02):  # Far from scan line
            for y in np.arange(0.2, 0.3, 0.02):
                for z in np.arange(0.35, 0.37, 0.02):
                    points.append([x, y, z])
        
        points = np.array(points)
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        
        # Apply clustering
        filtered_pcd = apply_voxel_clustering(pcd, scan_line_x, voxel_size)
        filtered_points = np.asarray(filtered_pcd.points)
        
        # Should only keep points from box 1
        self.assertGreater(len(filtered_points), 0)
        self.assertLess(len(filtered_points), len(points))
        # Should keep approximately the points from box 1
        self.assertLessEqual(len(filtered_points), box1_count)

    def test_connected_boxes_across_scan_line(self):
        """Test clustering with connected boxes spanning across the scan line."""
        scan_line_x = 0.5
        voxel_size = 0.05
        
        # Create a long box that spans across the scan line
        points = []
        for x in np.arange(0.3, 0.7, 0.02):  # Spans across scan line
            for y in np.arange(0.2, 0.3, 0.02):
                for z in np.arange(0.35, 0.37, 0.02):
                    points.append([x, y, z])
        
        points = np.array(points)
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        
        # Apply clustering
        filtered_pcd = apply_voxel_clustering(pcd, scan_line_x, voxel_size)
        filtered_points = np.asarray(filtered_pcd.points)
        
        # Should keep all points since they're all connected
        self.assertEqual(len(filtered_points), len(points))

    def test_empty_point_cloud(self):
        """Test clustering with empty point cloud."""
        scan_line_x = 0.5
        voxel_size = 0.05
        
        pcd = o3d.geometry.PointCloud()
        
        # Apply clustering
        filtered_pcd = apply_voxel_clustering(pcd, scan_line_x, voxel_size)
        filtered_points = np.asarray(filtered_pcd.points)
        
        # Should return empty point cloud
        self.assertEqual(len(filtered_points), 0)

    def test_single_point_near_scan_line(self):
        """Test clustering with a single point near the scan line."""
        scan_line_x = 0.5
        voxel_size = 0.05
        
        # Single point near scan line
        points = np.array([[0.51, 0.25, 0.36]])
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        
        # Apply clustering
        filtered_pcd = apply_voxel_clustering(pcd, scan_line_x, voxel_size)
        filtered_points = np.asarray(filtered_pcd.points)
        
        # Should keep the single point
        self.assertEqual(len(filtered_points), 1)
        np.testing.assert_array_almost_equal(filtered_points[0], points[0])

    def test_different_voxel_sizes(self):
        """Test clustering with different voxel sizes."""
        scan_line_x = 0.5
        
        # Create two boxes with a small gap
        points = []
        
        # Box 1: Near scan line
        for x in np.arange(0.48, 0.52, 0.01):
            for y in np.arange(0.2, 0.25, 0.01):
                for z in np.arange(0.35, 0.37, 0.01):
                    points.append([x, y, z])
        
        # Box 2: Small gap from box 1
        for x in np.arange(0.54, 0.58, 0.01):  # Small gap
            for y in np.arange(0.2, 0.25, 0.01):
                for z in np.arange(0.35, 0.37, 0.01):
                    points.append([x, y, z])
        
        points = np.array(points)
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        
        # Test with small voxel size (should separate boxes)
        filtered_pcd_small = apply_voxel_clustering(pcd, scan_line_x, 0.01)
        filtered_points_small = np.asarray(filtered_pcd_small.points)
        
        # Test with large voxel size (should connect boxes)
        filtered_pcd_large = apply_voxel_clustering(pcd, scan_line_x, 0.1)
        filtered_points_large = np.asarray(filtered_pcd_large.points)
        
        # Large voxel size should keep more points (both boxes connected)
        self.assertGreaterEqual(len(filtered_points_large), len(filtered_points_small))

    def test_noise_filtering(self):
        """Test that isolated noise points are filtered out."""
        scan_line_x = 0.5
        voxel_size = 0.05
        
        # Create main box near scan line
        points = []
        for x in np.arange(0.45, 0.55, 0.02):
            for y in np.arange(0.2, 0.3, 0.02):
                for z in np.arange(0.35, 0.37, 0.02):
                    points.append([x, y, z])
        
        main_box_count = len(points)
        
        # Add isolated noise points
        noise_points = [
            [0.1, 0.1, 0.1],   # Far away noise
            [0.9, 0.9, 0.9],   # Far away noise
            [0.3, 0.5, 0.8],   # Isolated noise
        ]
        points.extend(noise_points)
        
        points = np.array(points)
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        
        # Apply clustering
        filtered_pcd = apply_voxel_clustering(pcd, scan_line_x, voxel_size)
        filtered_points = np.asarray(filtered_pcd.points)
        
        # Should filter out noise points, keep only main box
        self.assertGreater(len(filtered_points), 0)
        self.assertLessEqual(len(filtered_points), main_box_count)
        self.assertLess(len(filtered_points), len(points))  # Some noise should be filtered


if __name__ == '__main__':
    unittest.main()
