from pprint import pprint
from typing import Generator, Optional

import numpy as np
import open3d as o3d
from loguru import logger
import tomlkit
from box_top.box_top_detector_protocol import BoxTopDetectorProtocol
from camera.camera_realsense import CameraProtocol, RealSenseCamera
from scipy.spatial.transform import Rotation as R

from box_top import BoxTop
from box_top.visualizer import NoOpVisualizer, VisualizerProtocol


def get_box_top(pcd, visualizer: Optional[VisualizerProtocol] = None):
    points = np.asarray(pcd.points)

    if len(points) < 10:
        logger.trace(f"Too few points in point cloud: {len(points)}")
        if visualizer is not None:
            visualizer.step()
        return None

    # Get top (high Z value) of box by taking 90th percentile of Z values
    z_values = points[:, 2]
    try:
        top_box_z = float(np.percentile(z_values, 90))
    except IndexError:
        logger.error(
            f"Failed to compute top of box (90th percentile Z), got {len(z_values)} points"
        )
        return None
    logger.trace(f"Top of box (90th percentile Z): {top_box_z:.3f} mm")

    # Isolate top of box
    pcd = pcd.crop(
        o3d.geometry.AxisAlignedBoundingBox(
            min_bound=(-np.inf, -np.inf, top_box_z - 0.1),
            max_bound=(np.inf, np.inf, top_box_z + 0.1),
        )
    )
    points = np.asarray(pcd.points)
    logger.trace(
        f"Min and max Z after cropping: {points[:, 2].min():.3f} to {points[:, 2].max():.3f} mm"
    )

    # Remove single points (noise)
    _, ind = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=0.68)
    # cl, ind = pcd.remove_radius_outlier(nb_points=30, radius=0.01)
    outlier_cloud = pcd.select_by_index(ind, invert=True)
    outlier_cloud.paint_uniform_color([1, 0, 0])
    pcd = pcd.select_by_index(ind)

    try:
        # Convert point cloud to tensor for guaranteed minimal oriented bounding box
        points_tensor = o3d.core.Tensor(np.asarray(pcd.points), dtype=o3d.core.float64)

        # Use tensor-based approach with MINIMAL_JYLANKI method for guaranteed minimal OBB
        obb_tensor = o3d.t.geometry.OrientedBoundingBox.create_from_points(
            points_tensor,
            robust=False,
            method=o3d.t.geometry.MINIMAL_JYLANKI,
        )

        # Convert back to legacy format for compatibility with existing code
        obb = obb_tensor.to_legacy()
    except RuntimeError as e:
        logger.error(f"Failed to compute OBB: {e}")
        return None

    logger.trace(f"OBB center: {obb.center}")
    logger.trace(f"OBB extent: {obb.extent}")
    logger.trace(f"OBB rotation matrix: {obb.R}")
    logger.trace(f"OBB volume: {obb.volume()}")

    # We want to have the longest axis of the OBB be the length: the first axis.
    # The second axis should be the width, and the third axis should be the height.
    # The shortest axis of the OBB is the height.
    obb_extent = obb.extent.copy()
    obb_R = obb.R.copy()
    if obb_extent[0] < obb_extent[1]:
        obb_extent = np.array([obb_extent[1], obb_extent[0], obb_extent[2]])
        obb_R = obb_R @ np.array([[0, 1, 0], [-1, 0, 0], [0, 0, 1]])
    if obb_extent[1] < obb_extent[2]:
        obb_extent = np.array([obb_extent[0], obb_extent[2], obb_extent[1]])
        obb_R = obb_R @ np.array([[1, 0, 0], [0, 0, 1], [0, -1, 0]])
    if obb_extent[0] < obb_extent[1]:
        obb_extent = np.array([obb_extent[1], obb_extent[0], obb_extent[2]])
        obb_R = obb_R @ np.array([[0, 1, 0], [-1, 0, 0], [0, 0, 1]])
    logger.trace(f"OBB extent after sorting: {obb_extent}")

    # Get rotation angles from rotation matrix
    r = R.from_matrix(obb_R)
    angles = r.as_euler("zxy", degrees=True)
    logger.trace(
        f"z rotation: {angles[0]:.3f} deg, x rotation: {angles[1]:.3f} deg, y rotation: {angles[2]:.3f} deg"
    )

    if visualizer is not None:
        # Use the injected visualizer
        visualizer.update(pcd, obb, outlier_cloud)

    return BoxTop(obb.center, top_box_z, obb_extent, angles, obb.volume())


class BoxTopDetector(BoxTopDetectorProtocol):
    """
    Box top detector class that processes point clouds from a camera to detect box top positions.
    Uses dependency injection for the camera and visualizer implementations.
    """

    def __init__(self, camera: CameraProtocol, visualizer: Optional[VisualizerProtocol] = None):
        """
        Initialize the BoxTopDetector with a camera and optional visualizer.

        Args:
            camera: Camera implementation following CameraProtocol
            visualizer: Optional visualizer implementation following VisualizerProtocol
        """
        with open("config/conveyor.toml", "r") as f:
            config = tomlkit.load(f)
        self.camera = camera
        self._should_stop = False
        self.transform = config["calibration"]["transform"].unwrap()  # type: ignore
        roi = config["calibration"]["roi"].unwrap()  # type: ignore
        self.ROI = o3d.geometry.AxisAlignedBoundingBox(
            min_bound=(0, 0, config["deadzone"]["deadzone_above_conveyor"].unwrap()),  # type: ignore
            max_bound=(roi[0], roi[1], config["boxes"]["max_box_height"].unwrap()),  # type: ignore
        )
        self.min_box_dimension = config["boxes"]["min_box_dimension"].unwrap()  # type: ignore

        # Initialize visualizer if provided
        if visualizer is None:
            self.visualizer = NoOpVisualizer()
        else:
            self.visualizer = visualizer

    def detect_box_tops(self) -> Generator[Optional[BoxTop], None, None]:
        """
        Start box top detection and yield BoxTop objects as a stream.

        Yields:
            BoxTop objects or None if detection fails
        """
        with self.camera, self.visualizer:
            while not self._should_stop:
                pcd = self.camera.get_pointcloud()
                if pcd is None:
                    yield None
                    continue

                # Apply calibration
                pcd = pcd.transform(self.transform).crop(self.ROI)

                box_position = get_box_top(pcd, visualizer=self.visualizer)

                # Check if visualizer wants to stop (e.g., escape key pressed)
                if self.visualizer.should_stop():
                    logger.trace("Visualizer requested stop")
                    self._should_stop = True

                if box_position is None:
                    continue
                if (
                    box_position.extent[0] < self.min_box_dimension
                    or box_position.extent[1] < self.min_box_dimension
                ):
                    logger.debug(f"Box too small: {box_position.extent}")
                    continue

                yield box_position

    def detect_single(self) -> Optional[BoxTop]:
        """
        Detect a single box top from the current camera frame.

        Returns:
            BoxTop object or None if detection fails
        """
        pcd = self.camera.get_pointcloud()
        if pcd is None:
            return None

        # Apply calibration
        pcd = pcd.transform(self.transform).crop(self.ROI)

        return get_box_top(pcd, visualizer=self.visualizer)


if __name__ == "__main__":
    from box_top.visualizer import Open3DVisualizer

    camera = RealSenseCamera(enable_temporal_filter=False)

    # Create visualizer for demonstration
    visualizer = Open3DVisualizer()
    box_top_detector = BoxTopDetector(camera, visualizer=visualizer)

    try:
        for box_top in box_top_detector.detect_box_tops():
            if box_top is not None:
                pprint(box_top)
    except KeyboardInterrupt:
        logger.info("Stopping detection...")
