from typing import Optional, Generator, Protocol

from box_top import BoxTop


class BoxTopDetectorProtocol(Protocol):
    """Protocol defining the interface for box top detector implementations."""

    def detect_box_tops(self) -> Generator[Optional[BoxTop], None, None]:
        """
        Start box top detection and yield BoxTop objects as a stream.

        Yields:
            BoxTop objects or None if detection fails
        """
        ...

    def detect_single(self) -> Optional[BoxTop]:
        """
        Detect a single box top.

        Returns:
            BoxTop object or None if detection fails
        """
        ...


class BoxDetectorProtocol(Protocol):
    """Protocol defining the interface for box detector implementations."""

    def detect_boxes(self) -> Generator[Optional[BoxTop], None, None]:
        """
        Start box detection and yield BoxTop objects as a stream.
        Only yields boxes when they cross the detection threshold.

        Yields:
            BoxTop objects or None if detection fails
        """
        ...
