# ========================== #
# Configuration part by user #
# ========================== #

[boxes]
min_box_dimension = 0.09 # 9 cm
max_box_height = 0.45    # 45 cm

[deadzone]
deadzone_above_conveyor = 0.09 # 9 cm

[markers]
marker_thickness = 0.002 # 2 mm
upstream_marker_id = 7
width_marker_id = 3
downstream_marker_id = 6


# ======================================================================================== #
# Values below are calculated automatically by running calibration/conveyor_calibration.py #
# ======================================================================================== #

[calibration]
transform = [
  [
    0.01617138534252347,
    0.999260401468933,
    0.034887481311357174,
    0.45662083821672417,
  ],
  [
    0.9998562807397092,
    -0.016338939227327473,
    0.004522933813517984,
    0.29597785592615505,
  ],
  [
    0.00508961309525418,
    0.03480932520277257,
    -0.9993810117855293,
    1.2036116986915941,
  ],
  [
    0.0,
    0.0,
    0.0,
    1.0,
  ],
]
roi = [0.9684602832982558, 0.549585346302091]
