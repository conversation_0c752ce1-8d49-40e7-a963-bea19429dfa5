#!/usr/bin/env python3
"""
Demonstration script showing the clustering functionality in box top detection.

This script creates synthetic point clouds with multiple boxes and demonstrates
how the clustering algorithm selects the box closest to the scan line.
"""

import numpy as np
import open3d as o3d
from loguru import logger

from box_top.box_top_detector import get_box_top, get_box_clusters


def create_box_point_cloud(center, size, num_points=200):
    """Create a point cloud representing a box."""
    # Generate random points within a box
    points = np.random.uniform(-0.5, 0.5, (num_points, 3))
    points *= size  # Scale by box size
    points += center  # Translate to center position
    
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    return pcd


def demo_single_box():
    """Demonstrate clustering with a single box."""
    logger.info("=== Demo: Single Box ===")
    
    # Create a single box
    box_pcd = create_box_point_cloud(
        center=np.array([0.5, 0.3, 0.8]), 
        size=np.array([0.2, 0.15, 0.1]), 
        num_points=200
    )
    
    scan_line = 0.456  # From config
    
    # Test clustering
    clusters = get_box_clusters(box_pcd)
    logger.info(f"Found {len(clusters)} clusters")
    
    # Test box detection
    box_top = get_box_top(box_pcd, scan_line=scan_line)
    if box_top:
        logger.info(f"Detected box at center: {box_top.center}")
        logger.info(f"Distance from scan line: {abs(box_top.center[0] - scan_line):.3f}")
    else:
        logger.info("No box detected")


def demo_two_boxes():
    """Demonstrate clustering with two boxes."""
    logger.info("\n=== Demo: Two Boxes ===")
    
    scan_line = 0.456  # From config
    
    # Create two boxes: one closer to scan line, one farther
    box1_center = np.array([0.4, 0.3, 0.8])  # Closer to scan line
    box2_center = np.array([0.8, 0.3, 0.8])  # Farther from scan line
    
    box1_pcd = create_box_point_cloud(
        center=box1_center,
        size=np.array([0.15, 0.15, 0.1]), 
        num_points=150
    )
    box2_pcd = create_box_point_cloud(
        center=box2_center,
        size=np.array([0.15, 0.15, 0.1]), 
        num_points=150
    )
    
    # Combine the point clouds
    combined_pcd = box1_pcd + box2_pcd
    
    logger.info(f"Box 1 center: {box1_center}, distance from scan line: {abs(box1_center[0] - scan_line):.3f}")
    logger.info(f"Box 2 center: {box2_center}, distance from scan line: {abs(box2_center[0] - scan_line):.3f}")
    
    # Test clustering
    clusters = get_box_clusters(combined_pcd)
    logger.info(f"Found {len(clusters)} clusters")
    
    # Test box detection - should return the box closest to scan line
    box_top = get_box_top(combined_pcd, scan_line=scan_line)
    if box_top:
        logger.info(f"Selected box at center: {box_top.center}")
        logger.info(f"Distance from scan line: {abs(box_top.center[0] - scan_line):.3f}")
        
        # Verify it selected the closer box
        dist_to_box1 = np.linalg.norm(box_top.center - box1_center)
        dist_to_box2 = np.linalg.norm(box_top.center - box2_center)
        
        if dist_to_box1 < dist_to_box2:
            logger.info("✓ Correctly selected Box 1 (closer to scan line)")
        else:
            logger.info("✓ Selected Box 2")
    else:
        logger.info("No box detected")


def demo_three_boxes():
    """Demonstrate clustering with three boxes."""
    logger.info("\n=== Demo: Three Boxes ===")
    
    scan_line = 0.456  # From config
    
    # Create three boxes at different distances from scan line
    box1_center = np.array([0.3, 0.3, 0.8])   # Farther from scan line
    box2_center = np.array([0.45, 0.3, 0.8])  # Very close to scan line
    box3_center = np.array([0.7, 0.3, 0.8])   # Farther from scan line
    
    box1_pcd = create_box_point_cloud(center=box1_center, size=np.array([0.12, 0.12, 0.08]), num_points=120)
    box2_pcd = create_box_point_cloud(center=box2_center, size=np.array([0.12, 0.12, 0.08]), num_points=120)
    box3_pcd = create_box_point_cloud(center=box3_center, size=np.array([0.12, 0.12, 0.08]), num_points=120)
    
    # Combine all point clouds
    combined_pcd = box1_pcd + box2_pcd + box3_pcd
    
    logger.info(f"Box 1 center: {box1_center}, distance from scan line: {abs(box1_center[0] - scan_line):.3f}")
    logger.info(f"Box 2 center: {box2_center}, distance from scan line: {abs(box2_center[0] - scan_line):.3f}")
    logger.info(f"Box 3 center: {box3_center}, distance from scan line: {abs(box3_center[0] - scan_line):.3f}")
    
    # Test clustering
    clusters = get_box_clusters(combined_pcd)
    logger.info(f"Found {len(clusters)} clusters")
    
    # Test box detection - should return box2 (closest to scan line)
    box_top = get_box_top(combined_pcd, scan_line=scan_line)
    if box_top:
        logger.info(f"Selected box at center: {box_top.center}")
        logger.info(f"Distance from scan line: {abs(box_top.center[0] - scan_line):.3f}")
        
        # Verify it selected the closest box
        distances = [
            (1, np.linalg.norm(box_top.center - box1_center)),
            (2, np.linalg.norm(box_top.center - box2_center)),
            (3, np.linalg.norm(box_top.center - box3_center))
        ]
        closest_box_num = min(distances, key=lambda x: x[1])[0]
        logger.info(f"✓ Selected Box {closest_box_num} (closest to scan line)")
    else:
        logger.info("No box detected")


def demo_clustering_parameters():
    """Demonstrate the effect of clustering parameters."""
    logger.info("\n=== Demo: Clustering Parameters ===")
    
    # Create two boxes that are close together
    box1_pcd = create_box_point_cloud(
        center=np.array([0.4, 0.3, 0.8]),
        size=np.array([0.1, 0.1, 0.08]), 
        num_points=100
    )
    box2_pcd = create_box_point_cloud(
        center=np.array([0.5, 0.3, 0.8]),  # Close to box1
        size=np.array([0.1, 0.1, 0.08]), 
        num_points=100
    )
    
    combined_pcd = box1_pcd + box2_pcd
    
    # Test different eps values
    for eps in [0.02, 0.05, 0.1, 0.2]:
        clusters = get_box_clusters(combined_pcd, eps=eps, min_points=10)
        logger.info(f"eps={eps}: Found {len(clusters)} clusters")


if __name__ == "__main__":
    logger.info("Box Top Detection Clustering Demo")
    logger.info("=" * 50)
    
    # Set random seed for reproducible results
    np.random.seed(42)
    
    demo_single_box()
    demo_two_boxes()
    demo_three_boxes()
    demo_clustering_parameters()
    
    logger.info("\n" + "=" * 50)
    logger.info("Demo completed!")
